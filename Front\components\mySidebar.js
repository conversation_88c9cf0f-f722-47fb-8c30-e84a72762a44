// 侧边栏组件
const DynamicSidebar = {
  init: function (config) {
    const {
      data,
      containerSelector = ".mySidebar",
      activeItem = null,
    } = config;

    const container = document.querySelector(containerSelector);
    if (!container) {
      console.warn(`找不到侧边栏容器: ${containerSelector}`);
      return false;
    }

    if (!Array.isArray(data) || data.length === 0) {
      console.error("侧边栏数据必须是非空数组");
      return false;
    }

    this.generateSidebar(container, data, activeItem);
    return true;
  },

  generateSidebar: function (container, data, activeItem) {
    // 处理标题
    const titleItem = data[0];
    const titleText =
      typeof titleItem === "object" ? titleItem.text : titleItem;
    const titleLink = typeof titleItem === "object" ? titleItem.link : "#";
    const titleHTML =
      titleLink !== "#"
        ? `<h2 class="title" onclick="toRouter(this)" data-link="${titleLink}">${titleText}</h2>`
        : `<h2 class="title">${titleText}</h2>`;

    // 处理菜单项
    const menuItems = data
      .slice(1)
      .map((item, index) => {
        const itemText = typeof item === "object" ? item.text : item;
        const itemLink = typeof item === "object" ? item.link : "#";
        const isActive = activeItem ? itemText === activeItem : index === 0;
        const activeClass = isActive ? " textSelect" : "";

        return `<li class="${activeClass.trim()}"
                        onclick="DynamicSidebar.handleClick(this)"
                        data-link="${itemLink}"
                        data-menu-item="${itemText}">
                        ${itemText}
                    </li>`;
      })
      .join("");

    const sidebarHTML = `
            <ul>
                ${titleHTML}
                ${menuItems}
            </ul>
        `;

    container.innerHTML = sidebarHTML;
  },

  // 处理点击事件
  handleClick: function (element) {
    // 移除所有激活状态
    const allItems = element.parentElement.querySelectorAll("li");
    allItems.forEach((item) => item.classList.remove("textSelect"));

    // 给当前点击的元素添加激活状态
    element.classList.add("textSelect");

    // 执行跳转
    toRouter(element);
  },
};

// 页面加载时初始化
document.addEventListener("DOMContentLoaded", () => {
  // 使用页面中的data数组
  if (typeof data !== "undefined" && Array.isArray(data)) {
    DynamicSidebar.init({
      data: data,
      containerSelector: ".mySidebar",
    });
  } else {
    console.error("未找到data数组或data不是数组类型");
  }
});
