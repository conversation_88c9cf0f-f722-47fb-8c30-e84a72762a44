/* 项目基础公共样式  */
*{
    margin: 0;
    padding: 0;
}
body{
    margin: 0;
    padding: 0;
    color: #2E2E2E;
}
:root {
  --myBlue:#0E6AF2;
  --myBlueBg:#0E6AF21A;
  --myBorder:#bbbbbb;
  --mask-bg: #0f0f204f;
  --mask1: rgba(0, 0, 0, 0.1);
  --mask2: rgba(0, 0, 0, 0.3);
  --mask3: rgba(0, 0, 0, 0.5);
  --mask4: rgba(0, 0, 0, 0.7);
  /* default */
  --bg-home: #ebebf0;
  /* 状态面板背景 */
  --bg-mainBox1: linear-gradient(#2C79E8 5%, #EFF1F5 90%);
  --bg-mainBox2: linear-gradient(#D8E6FB 1%, #EFF1F5 99%);
  --card-border: transparent;
  /* 登录面板背景 */
  --background-login: radial-gradient(
    circle,
    rgba(5, 2, 47, 1) 28%,
    rgba(55, 41, 152, 1) 86%,
    rgba(54, 40, 151, 1) 110%
  );
  --card-title-bg:white;
  --bg-aside: #242424;
  --primary-color: #4bd9cc;
  --primary:#4bd9cc; 

  --bk:rgb(0,0,0);
  --gray:#2c2c3a;
  --white:white;
  --black:rgb(50,50,50);
  --bg-head:white;
  --text-shadow:0 0 4px #ccc;
  --box-shadow:0 0 10px #ccc;
  --grey-bg:#f2f6f7;
  --card-body-bg:white;
  --card-footer-bg:white;
  --error-bg:#ffe9ee;
  --text-color:#2E2E2E;
  --text-color1:#676767;
  --text-color2:#888888;
  --text-color3:#666666;
  --text-color4: #D9D9D9;
  /* color -light - ui*/
  --warn:#fbbd08;
  --success:#39b54a;
  --success-light:#8dc63f;
  --info:#8799a3;
  --blue:#0081ff;
  --ocean:#02b6d2;
  --blue-deep:#2C79E8;
  /*  #5272F7*/
  /** 天青 */
  --cyan:#1cbbb4;
  --danger:#f56c6c;
  --warning:#f37b1d;
  --warm:#f5a623;
  --pink:#F9D7EA;
  --pink-deep:#E03997;
  --line:#d5e1e7d1;
  --red:#FE3846;
  --red-light:#f56c6c;


  --success-light:#7ef68e54;
  --blue-light:#65e6ff5c;
  --blue-text:#4190dd;
  --danger-light:#5a1e32;
  --danger-text:#f56c6c;
  /** 几乎纯白 */
  --info-light:#EEF1FE;
  --info-text-light:#3E4A79;
  --info-text:#666666;
  --warn-light:#fff2be66;

  --common-text:#212e63;
  --dialog-bg:#ffffff;
  --dialog-head-bg:#F2F2F7;

  --lineLight:#e7e9e9;
  --wx-danger:#CC5353;
  --wifi-1:#02b6d2;
  --wifi-0:#b9b9bd;
  --bd:#F0F2F7;
  --hlk-red:#FE3846;
  --hlk-red-light:#f56c6c;
}
.pageBox{
    width: 100%;
    /* height: 100%; */
    /* max-width: 1920px; */
    margin: auto;
    position: relative;
    box-sizing: border-box;
    /* border: 1px solid ; */
}
.redmi{
    color: var(--danger);
    cursor: pointer;
}
.redmi:hover{
    text-decoration: underline;
}
.hover:hover{
    color: var(--blue-deep);
    cursor: pointer;
}
.blue{
    color: var(--blue-deep);
}
.bd{
    border: 1px solid ;
}

.flex{
    display: flex;
    justify-content: center;
    place-items: center;
}
.column{
    display: flex;
    flex-direction: column;
    justify-content: center;
    place-items: center;
}
a{
    text-decoration: none;
    color: inherit;
    cursor: pointer;
}
.layui-layer-content{
    /* display: none; */
}
/* .input:focus{
    border:1px solid #f0f3f8 !important;
} */
.input{
    width: 80%;
    height: 2vw;
    max-height: 35px;
    min-height: 30px;
    line-height: 35px;
    padding: 2px 15px;
    letter-spacing: 1px;
    /* border-radius: 15px; */
    border: 1px solid rgb(238, 238, 238);
    background-color: var(--bd);
    transition: all .2s;
    outline: none;
}
.input:focus{
    border:1px solid #9eb5d6;
}
.input:hover{
    border:1px solid #9eb5d6;
}
.input::placeholder{
    font-size: 13px;
}
.select{
    padding: 10px;
    /* width: calc(80% - 8px); */
    border: none;
    outline: none;
    color: var(--text-color);
    cursor: pointer;
    border: 1px solid var(--bd);
    background-color: var(--bd);
}
button{
    cursor: pointer;
}
.button{
    min-width: 60px;
    padding: .3vw .5vw;
    background-color: var(--info-light);
    border: 1px solid var(--line);
    border-radius: 7px;
    transition: all .3s;
    font-size: 15px;
    min-height: 30px;
    min-width: 60px;
}
.button_blue{
    margin-left: .4vw;
    border-radius: 3px;
    background-color: var(--blue-deep);
    color: white;
}
.button_white{
    margin-left: .4vw;
    border-radius: 3px;
    background-color: white;
    color: var(--text-color);
}
.button_red{
    margin-left: .4vw;
    border-radius: 3px;
    background-color: #fe384528;
    color: #FE3846;
    border: 1px solid #FE3846;
}
.button_red:hover{
    color: var(--wx-danger);
}
button:hover{
    color: #e1e4ec;
}
.hoverRed:hover{
    color: var(--red);
    cursor: pointer;
}

.hoverBlue:hover{
    color: var(--blue-deep) !important;
    cursor: pointer;
}

.line{
    border-left: 1px solid var(--line);
    height: 23px;
    margin-left: 5px;
    /* padding: 2px; */
}
.line2{
    border-left: 1px solid #b4c2c4;
    height: 23px;
    margin-left: 5px;
    /* padding: 2px; */
}
.unline{
    color: var(--blue-deep);
    text-decoration: underline;
}
.load{
    animation: load 1.5s linear infinite;
}

.textSelect{
    color: dodgerblue !important;
}
.bgSelect{
    background-color: var(--blue-deep) !important;
    color: white;
}
.titleSelect{
    color: var(--blue-deep) !important; 
    padding-bottom: 5px;
    position: relative;
    user-select: none;
    /* border: 1px solid ; */
}
.titleSelect::before{
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    content: '';
    width: 40%;
    height: 2px;
    background-color: var(--blue-deep);
}
.primary{
    color: var(--blue-deep);
}
.success{
    color: var(--success);
}
.red{
    color: crimson;
}
.red3{
    color: var(--wx-danger);
}
.red2{
    color: var(--danger-text);
}
.red1{
    color: var(--red);
}
.warning{
    color: var(--warning);
}
.gray{
    color: gray;
}
.textOver{
    /* width: 100%; */
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
}
.textOver2{
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
    text-overflow: ellipsis;
}
.textOver3{
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
    overflow: hidden;
    text-overflow: ellipsis;
}
.more{
    margin-left: auto;
    margin-right: 20px;
    font-size: 13px;
    text-decoration: underline;
    white-space: nowrap;
    color: var(--info);
    cursor: pointer;
}
.hide{
    display: none;
}
.show{
    display: block;
}
.pointer{
    cursor: pointer;
}
.money{
    color: var(--red);
    letter-spacing: 2px;
    /* font-weight: bold; */
}
.textL{
    text-align: left;
}
.textC{
    text-align: center;
}
.textR{
    text-align: right;
}
.borderB{
    border-bottom: 1px solid var(--line);
}
.image{
    margin-left: 5%;
    width: 90%;
    height: 90%;
    object-fit: contain;
}
.copy{
    color: var(--blue-deep);
    cursor: pointer;
    margin-left: 5px;
    margin-right: 5px;
    font-size: 16px;
}
.layui-btn{
    width: 80%;
}
.main {
    margin: 0 auto;
    width: 80%;
    overflow-x: hidden;
    padding-bottom: 50px;
    position: relative;
    /* border: 1px solid #000; */
}

/* 面包屑 --常用公共模块 */
.breadBox{
    padding: .8vw 10px;
    font-size: 15px;
    display: flex;
    border-bottom: 1px solid var(--line);
}
.breadBox>div{
    color: var(--text-color3);
    cursor: pointer;
    line-height: 30px;
}
.breadBox>div:nth-child(2n){
    padding: 0px 4px;
}

/* 产品推荐 --常用公共模块 */
.productComment{
    padding: .2vw 0px 1.5vw 0px; 
    border-bottom: 1px solid var(--line);
    /* border: 2px solid red; */
}
.productComment>.title{
    margin-top: 5vw;
    border-top: 1px solid var(--line);
    padding: 1.5vw 0px .3vw 0px;
    font-size: 20px !important;
    color: var(--text-color3);
}
.mainBox2_container {
    padding: .5vw 0px;
    /* width: 100%; */
    display: flex;
    justify-content: space-between;
    /* border: 2px solid ; */
}

.mainBox2_content {
    position: relative;
    display: flex;
    justify-content: space-evenly;
    max-width: 14%;
    min-height: 265px;
    flex-direction: column;
    background-color: white;
    padding: 0.4vw;
    border-radius: 5px;
    border: 1px solid rgb(230, 230, 230);
    /* border: 2px solid ; */
    /* overflow: auto; */
}

.mainBox2_content>div {
    flex: 1;
    padding: 0.2vw 0px 0px 0px;
    /* border: 1px solid ; */
    display: flex;
    place-items: center;
}
.mainBox2_content>div>img {
    width: 100%;
    height: 100%;
    object-fit: fill;
}
.mainBox2_content>div:nth-child(1){
    max-height: 60%;
}
/* 34人购买 */
.mainBox2_content>div:nth-child(3) {
    font-size: 12px;
}
.mainBox2_content>div:nth-child(3)>.icon-star{
    margin-left: 1vw;
    color: var(--warning);
}

.mainBox2_content_desc {
    margin-top: 5px;
    /* width: 90%; */
    font-size: .9em;
    font-weight: 380;
    letter-spacing: 1px;
    word-break: break-all;
    color: #666666;
    letter-spacing: 2px;
    /* border: 1px solid ; */
}

.mainBox2_content_price {
    margin-top: 2px;
    font-size: 1vw;
}
/* 产品推荐 --常用公共模块 -结束 */
.textarea:before {
    content: attr(data-currentCount);
    position: absolute;
    right: 48px;
    bottom: 2px;
    color: #ccc;
    z-index: 100;
    font-size: .7vw;
    /* background-color: red; */
}

.textarea::after {
    content: attr(data-maxLength);
    position: absolute;
    right: 13px;
    bottom: 2px;
    color: #666666;
    z-index: 100;
    letter-spacing: 1px;
    font-size: .7vw;
    /* background-color: blue; */
}

.bug{
    margin-top: auto;
    height: 20vh;
}
.icon{
    cursor: pointer;
    font-size: 1vw;
}
.icon:hover{
    color: var(--blue-deep);
}
/* 大家开始都叫 isLogin? 登陆后会统一把data-login="false" 的isLogin 换成 unLogin */
.hadLogin{
    display: block !important;
}
.hadLogin[data-login_flex="true"]{
    display: flex !important;
}
.unLogin{
    display: none;
}
/* 分页器 */
#pagingBox{
    margin-top: 5vh;
}

/* 计算器 */
.computer {
    width: 5vw;
    min-width: 80px;
    border: 1px solid var(--text-color4);
    border-radius: 45px;
    display: flex;
    padding: .25vw !important;
}

.computer>div {
    flex: 1;
    text-align: center;
}

.computer>div:nth-child(2n-1):hover {
    color: var(--blue-deep);
}

/* 必填项 */
.required{
    position: relative;
}
.required::before{
    content: '*';
    color: red;
    position: absolute;
    margin-left: -10px;
    margin-top: 2px;
    font-weight: bold;
    font-size: 17px;
}
/* 蒙层弹窗  -开始*/
.mask{
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    width: 100%;
    background-color: #1616164b;
    z-index: 1000;
    border: none;
    /* display: none; */
}
.showNotesbox{
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 450px;
    height: 350px;
    background: #fff;
    color: var(--text-color);
    text-align: center;
    padding: 20px 0;
    border-radius: 18px;
}
.dialog_btnBox{
    display: flex;
    flex-direction: column;
    margin-top: 30px;
    margin-left: 25%;
    width: 50%;
}
.dialog_titleText{
    font-size: 25px;
    padding-bottom: 5px;
}
.dialog_btnBox>.button{
    border-radius: 45px;
    margin-bottom: 10px;
    height: 40px;
}
.dialog_btnBox>.btn1{
    background-color: rgba(44,121,232,0.15);
    color: #2C79E8;
}
.showNotesbox>.close{
    position: absolute;
    top: 0px;
    right: 10px;
    font-size: 30px;
    cursor: pointer;
    color: #C4C6CF;
    transition: all .5s;
}
.showNotesbox>.close:hover{
    color: #000;
}
/* 蒙层弹窗  -结束*/
/* 购物车图标 */
.icon-icon1{
    position: absolute;
    top: 100px;
    right: 1vw;
    font-size: 2vw;
    /* border: 1px solid ; */
    background-color: white;
    box-shadow: 4px 4px 8px 0px rgba(0,0,0,0.1), -4px -4px 8px 0px rgba(0,0,0,0.1);
    padding: 5px !important;
    border-radius: 50%;
}
.icon-icon1:hover{
    color: var(--warn);
    cursor: pointer;
}
/* 在线客服图标 */
.icon-zaixiankefu1{
    font-size: 1.2vw;
    position: relative;
}
.icon-zaixiankefu1:hover{
    cursor: pointer;
    color: var(--blue-deep);
}
.icon-zaixiankefu1:hover::after{
    opacity: 1;
    left: 120%;
}
.icon-zaixiankefu1::after{
    content: '在线客服';
    width: 300%;
    position: absolute;
    left: 0%;
    padding: 0 .2vw;
    top: 12%;
    text-align: left;
    font-size: .8vw;
    opacity: 0;
    transition: all 1s;
    /* border: 1px solid ; */
}
/** 删除icon */
.icon-icdelete:hover{
    cursor: pointer;
    color: red;
}
.layui-input-inline{
    width: 35%;
}

/* 加载 */
@keyframes load {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}