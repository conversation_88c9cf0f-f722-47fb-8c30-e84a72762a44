/**
 * 侧边栏组件
 * 整合了动态侧边栏和静态侧边栏的功能
 */


 
// 动态侧边栏管理器
const DynamicSidebar = {
  /**
   * 初始化侧边栏
   * @param {Object} config - 配置对象
   * @param {Array} config.data - 菜单数据数组，每个元素可以是字符串或对象 {text: '显示文本', link: '链接地址'}
   * @param {string} config.containerSelector - 容器选择器，默认为'.mySidebar'
   * @param {Function} config.onItemClick - 菜单项点击回调函数
   * @param {string} config.activeItem - 默认激活的菜单项
   */
  init: function(config) {
      const {
          data,
          containerSelector = '.mySidebar',
          onItemClick = null,
          activeItem = null
      } = config;

      const container = document.querySelector(containerSelector);
      
      if (!container) {
          console.warn(`找不到侧边栏容器: ${containerSelector}`);
          return false;
      }

      if (!Array.isArray(data) || data.length === 0) {
          console.error('侧边栏数据必须是非空数组');
          return false;
      }

      this.generateSidebar(container, data, onItemClick, activeItem);
      return true;
  },

  /**
   * 生成侧边栏HTML
   * @param {Element} container - 容器元素
   * @param {Array} data - 菜单数据
   * @param {Function} onItemClick - 点击回调
   * @param {string} activeItem - 激活项
   */
  generateSidebar: function(container, data, onItemClick, activeItem) {
      // 处理标题
      const titleItem = data[0];
      const titleText = typeof titleItem === 'object' ? titleItem.text : titleItem;
      const titleLink = typeof titleItem === 'object' ? titleItem.link : '#';
      const titleHTML = titleLink !== '#' ? 
          `<h2 onclick="DynamicSidebar.handleItemClick(this, '${titleText}', ${onItemClick ? 'true' : 'false'}, '${titleLink}')" style="cursor: pointer;">${titleText}</h2>` :
          `<h2>${titleText}</h2>`;

      // 处理菜单项
      const menuItems = data.slice(1).map((item, index) => {
          const itemText = typeof item === 'object' ? item.text : item;
          const itemLink = typeof item === 'object' ? item.link : '#';
          const isActive = activeItem ? itemText === activeItem : index === 0;
          console.log('isActive', isActive)
          const activeClass = isActive ? ' class="textSelect"' : '';
          
          return `<li${activeClass} 
                      onclick="DynamicSidebar.handleItemClick(this, '${itemText}', ${onItemClick ? 'true' : 'false'}, '${itemLink}')" 
                      data-menu-item="${itemText}">
                      ${itemText}
                  </li>`;
      }).join('');

      const sidebarHTML = `
          <ul>
              ${titleHTML}
              ${menuItems}
          </ul>
      `;

      container.innerHTML = sidebarHTML;
      console.log(`动态侧边栏生成完成: ${titleText}，包含 ${data.length - 1} 个菜单项`);
  },

  /**
   * 处理菜单项点击事件
   * @param {Element} element - 被点击的元素
   * @param {string} menuItem - 菜单项名称
   * @param {boolean} hasCallback - 是否有自定义回调
   * @param {string} link - 链接地址
   */
  handleItemClick: function(element, menuItem, hasCallback, link) {
      // 更新选中状态（如果不是标题）
      if (element.tagName.toLowerCase() === 'li') {
          this.updateActiveState(element);
      }
      
      console.log('侧边栏菜单项被点击:', menuItem);

      // 如果有链接，进行跳转
      if (link && link !== '#') {
          // 在跳转前保存当前激活项到 sessionStorage
          sessionStorage.setItem('activeSidebarItem', menuItem);
          window.location.href = link;
      }

      // 如果有自定义回调，触发全局事件
      if (hasCallback) {
          const event = new CustomEvent('sidebarItemClick', {
              detail: { menuItem, element, link }
          });
          document.dispatchEvent(event);
      }
  },

  /**
   * 更新激活状态
   * @param {Element} activeElement - 当前激活的元素
   */
  updateActiveState: function(activeElement) {
      // 移除同级所有元素的激活状态
      const allItems = activeElement.parentElement.querySelectorAll('li');
      allItems.forEach(item => item.classList.remove('textSelect'));
      
      // 添加当前元素的激活状态
      activeElement.classList.add('textSelect');
  },

  /**
   * 设置激活项
   * @param {string} menuItem - 要激活的菜单项名称
   * @param {string} containerSelector - 容器选择器
   */
  setActiveItem: function(menuItem, containerSelector = '.mySidebar') {
      const container = document.querySelector(containerSelector);
      if (!container) return;

      const targetItem = container.querySelector(`[data-menu-item="${menuItem}"]`);
      console.log('targetItem', targetItem)
      if (targetItem) {
          this.updateActiveState(targetItem);
      }
  },

  /**
   * 获取当前激活的菜单项
   * @param {string} containerSelector - 容器选择器
   * @returns {string|null} 当前激活的菜单项名称
   */
  getActiveItem: function(containerSelector = '.mySidebar') {
      const container = document.querySelector(containerSelector);
      if (!container) return null;

      const activeItem = container.querySelector('li.textSelect');
      console.log('activeItem', activeItem)
      return activeItem ? activeItem.getAttribute('data-menu-item') : null;
  }
};

// 页面专用处理函数
function handleSidebarItemClick(event) {
  const { menuItem, link } = event.detail;

  console.log('菜单项被点击:', menuItem);

  // 如果已经有链接，直接使用链接进行跳转
  if (link && link !== '#') {
      window.location.href = link;
      return;
  }

  // 如果没有链接，使用默认路由
  switch(menuItem) {
      case '应用支持':
          window.location.href = '/appSupport';
          break;
      case '焦点专题':
          window.location.href = '/appSupport/focus';
          break;
      case '资料下载':
          window.location.href = '/appSupport/downloads';
          break;
      case '应用视频':
          window.location.href = '/appSupport/videos';
          break;
      case '常见问题':
          window.location.href = '/appSupport/faq';
          break;
      case '样品申请':
          window.location.href = '/appSupport/sample';
          break;
      case '成品检测报告':
          window.location.href = '/appSupport/reports';
          break;
      default:
          console.log('未处理的菜单项:', menuItem);
  }
}

// 初始化页面的侧边栏
function initAppSupportSidebar() {
  // 检查动态侧边栏组件是否已加载
  if (typeof window.DynamicSidebar === 'undefined') {
      console.warn('动态侧边栏组件未加载，延迟重试');
      setTimeout(initAppSupportSidebar, 100);
      return;
  }

  // 使用页面中定义的data数组
  if (typeof data !== 'undefined' && Array.isArray(data)) {
      // 根据当前URL路径确定激活项
      const currentPath = window.location.pathname;
      let activeItem = '应用支持'; // 默认激活项
      
      if (currentPath.includes('/focus')) {
          activeItem = '焦点专题';
      } else if (currentPath.includes('/downloads')) {
          activeItem = '资料下载';
      } else if (currentPath.includes('/videos')) {
          activeItem = '应用视频';
      } else if (currentPath.includes('/faq')) {
          activeItem = '常见问题';
      } else if (currentPath.includes('/sample')) {
          activeItem = '样品申请';
      } else if (currentPath.includes('/reports')) {
          activeItem = '成品检测报告';
      }

      const success = window.DynamicSidebar.init({
          data: data,
          containerSelector: '.mySidebar',
          onItemClick: true, // 启用自定义点击处理
          activeItem: activeItem // 根据URL设置激活项
      });

      if (success) {
          console.log('侧边栏初始化成功，当前激活项:', activeItem);
      }
  } else {
      console.error('未找到data数组或data不是数组类型');
  }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
  console.log('侧边栏组件加载完成');

  // 加载侧边栏组件
  const productizedElement = document.querySelector('.mySidebar');
  if (productizedElement) {
      // 检测当前页面类型，决定加载哪个侧边栏
      const currentPath = window.location.pathname;
      const paths = ['/appSupport', '/product','/AppSupport'];
      
      // 检查当前路径是否匹配任何支持路径
      const pathHas = paths.some(path => currentPath.includes(path));
      if (pathHas) {
          // 页面，初始化动态侧边栏
          console.log('检测到页面，初始化动态侧边栏');
          setTimeout(() => {
              initAppSupportSidebar();
              
              // 从 sessionStorage 中恢复激活状态
              const savedActiveItem = sessionStorage.getItem('activeSidebarItem');
              if (savedActiveItem) {
                  window.DynamicSidebar.setActiveItem(savedActiveItem);
                  // 清除存储的状态
                  sessionStorage.removeItem('activeSidebarItem');
              }
          }, 200);

          // 监听侧边栏菜单项点击事件
          document.addEventListener('sidebarItemClick', handleSidebarItemClick);
      } else {
          // 产品页面，加载静态的产品分类侧边栏
          fetch('../../components/mySidebar.html')
              .then(response => {
                  console.log('fetch响应状态:', response.status);
                  if (!response.ok) {
                      throw new Error('网络响应不正常，状态码: ' + response.status);
                  }
                  return response.text();
              })
              .then(data => {
                  productizedElement.innerHTML = data;
                  
                  // 从 sessionStorage 中恢复激活状态
                  const savedActiveItem = sessionStorage.getItem('activeSidebarItem');
                  if (savedActiveItem) {
                      window.DynamicSidebar.setActiveItem(savedActiveItem);
                      // 清除存储的状态
                      sessionStorage.removeItem('activeSidebarItem');
                  }
              })
              .catch(error => {
                  console.error('加载productized.html时出错:', error);
              });
      }
  }
});

// 将DynamicSidebar暴露到全局作用域
window.DynamicSidebar = DynamicSidebar;

