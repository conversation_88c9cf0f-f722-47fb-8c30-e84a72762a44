﻿using B2B2CShop.Dto;
using DH.Entity;
using HlktechPower.Dto;
using Magicodes.ExporterAndImporter.Core;
using Magicodes.ExporterAndImporter.Excel;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;

using NewLife;
using NewLife.Data;
using NewLife.Log;
using Pek;
using Pek.DsMallUI;
using Pek.DsMallUI.Common;
using Pek.Models;
using Pek.NCubeUI.Areas.Admin;
using Pek.NCubeUI.Common;

using System.ComponentModel;
using System.Dynamic;

using XCode;
using XCode.Membership;

namespace HlktechPower.Areas.Admin.Controllers;

/// <summary>
///语言包
/// </summary>
[DisplayName("语言包")]
[Description("用于系统多语言的各种语言包")]
[AdminArea]
[DHMenu(49, ParentMenuName = "Settings", CurrentMenuUrl = "~/{area}/Languages", CurrentMenuName = "LanguagesList", CurrentIcon = "&#xe71f;", LastUpdate = "20250527")]
public class LanguagesController : PekCubeAdminControllerX {
    /// <summary>
    /// 语言列表
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("语言列表")]
    public IActionResult Index(string lanKey, string lanValue, int cultureId = -1, int page = 1)
    {
        dynamic viewModel = new ExpandoObject();
        var pages = new PageParameter()
        {
            PageIndex = page,
            PageSize = 10,
            RetrieveTotalCount = true,
            Sort = "Id",
            Desc = true
        };

        lanKey = lanKey.SafeString().Trim();

        var cultureList = Language.FindAllWithCache().Where(e => e.Status).OrderBy(e => e.DisplayOrder);
        ViewBag.LanguageList = cultureList.Select(item => new SelectListItem { Value = item.Id.ToString(), Text = WorkingLanguage.UniqueSeoCode == "en" ? item.EnglishName : item.Name, Selected = item.Id == Language.FindByDefault()?.Id });
        var List = LocaleStringResource.Search(lanKey, cultureId, lanValue.SafeString().Trim(), pages);

        viewModel.list = List;
        viewModel.page = page;
        viewModel.lanKey = lanKey;
        viewModel.lanValue = lanValue;
        viewModel.cultureId = cultureId;

        viewModel.PageHtml = PageHelper.CreatePage(page, pages.TotalCount, pages.PageCount, Url.Action("Index"), new Dictionary<string, string> { { "LanKey", lanKey }, { "CultureId", cultureId.ToString() }, { "LanValue", lanValue } });
        return View(viewModel);
    }

    /// <summary>添加语言包</summary>
    /// <returns></returns>
    [DisplayName("添加语言包")]
    [EntityAuthorize(PermissionFlags.Insert)]
    public IActionResult AddLanguages()
    {
        ViewBag.LanguageList = Language.FindAllWithCache().Where(e => e.Status).OrderBy(e => e.DisplayOrder);
        return View();
    }

    /// <summary>添加语言包</summary>
    /// <returns></returns>
    [DisplayName("添加语言包")]
    [HttpPost]
    [EntityAuthorize(PermissionFlags.Insert)]
    public IActionResult AddLanguages(string LanKey)
    {
        var lists = Language.FindAllWithCache().Where(e => e.Status);
        if (LanKey.IsNullOrEmpty())
        {
            ViewBag.languageList = lists.OrderBy(e => e.DisplayOrder);
            return Prompt(new PromptModel { Message = GetResource("翻译键不能为空") });
        }
        var List = new List<LocaleStringResource>();

        LanKey = LanKey.SafeString().Trim();
        foreach (var item in lists)
        {
            var model = new LocaleStringResource
            {
                LanKey = LanKey,
                CultureId = item.Id,
                LanValue = GetRequest($"[{item.Id}].LanValue")
            };
            if (!model.LanValue.IsNullOrEmpty())
            {
                List.Add(model);
            }
        }
        List.Insert(true);
        LocaleStringResource.Meta.Cache.Clear("");

        return Prompt(new PromptModel { Message = GetResource("保存成功"), IsOk = true, BackUrl = Url.Action("Index")! });
    }

    /// <summary>编辑语言包</summary>
    /// <returns></returns>
    [DisplayName("编辑语言包")]
    [EntityAuthorize(PermissionFlags.Update)]
    public IActionResult EditLanguages(string Id)
    {
        Id = Uri.UnescapeDataString(Id);
        ViewBag.LanguageList = Language.FindAllWithCache().Where(e => e.Status).OrderBy(e => e.DisplayOrder);
        var list = LocaleStringResource.FindByLanKey(Id);
        ViewBag.LanKey = list[0].LanKey;
        return View(list);
    }

    /// <summary>编辑语言包</summary>
    /// <returns></returns>
    [DisplayName("编辑语言包")]
    [HttpPost]
    [EntityAuthorize(PermissionFlags.Update)]
    public IActionResult EditLanguage(string LanKey)
    {
        if (LanKey.IsNullOrEmpty())
        {
            return Prompt(new PromptModel { Message = GetResource("翻译键不能为空") });
        }

        LanKey = LanKey.SafeString().Trim();

        var list = Language.FindAllWithCache().Where(e => e.Status);
        using (var tran1 = Language.Meta.CreateTrans())
        {
            foreach (var item in list)
            {
                var cultureId = GetRequest($"[{item.Id}].Id").ToInt();
                var lanKey = GetRequest($"[{item.Id}].LanKey").SafeString().Trim();
                var model = LocaleStringResource.FindByLanKeyAndCultureId(lanKey.SafeString().Trim(), cultureId);
                if (model == null)
                {
                    model = new LocaleStringResource
                    {
                        LanKey = LanKey,
                        CultureId = item.Id,
                        LanValue = GetRequest($"[{item.Id}].LanValue").SafeString().Trim()
                    };
                    if (!model.LanValue.IsNullOrEmpty())
                    {
                        model.Insert();
                    }
                }
                else
                {
                    model.LanKey = LanKey;
                    model.LanValue = GetRequest($"[{item.Id}].LanValue").SafeString().Trim();
                    model.Update();
                }
            }
            tran1.Commit();
        }
        LocaleStringResource.Meta.Cache.Clear("");

        return Prompt(new PromptModel { Message = GetResource("保存成功"), IsOk = true, BackUrl = Url.Action("Index")! });
    }

    /// <summary>
    /// 语言包数据删除
    /// </summary>
    /// <param name="Ids"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Delete)]
    [DisplayName("语言包数据删除")]
    public IActionResult Delete(string Ids)
    {
        var res = new DResult();
        LocaleStringResource.DelByIds(Ids.Trim(','));

        res.success = true;
        res.code = 10000;
        res.msg = GetResource("删除成功");
        return Json(res);
    }
    /// <summary>
    /// 导出翻译项
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize((PermissionFlags)16)]
    [DisplayName("导出")]
    public async Task<IActionResult> Export(Int32 cultureId)
    {
        var pages = new PageParameter()
        {
            PageIndex = 1,
            PageSize = 0,
            RetrieveTotalCount = true,
            OrderBy = "Id desc"
        };

        IExporter exporter = new ExcelExporter();

        var defaultLanguage = Language.FindByDefault();

        var modelLanguage = Language.FindById(cultureId);

        var list = LocaleStringResource.Search("", defaultLanguage.Id, "", pages).Select(e => new LocalStringExport { LanKey = e.LanKey, LanValue = e.LanValue, Culture = modelLanguage?.Name, TransValue = LocaleStringResource.FindByLanKeyAndCultureId(e.LanKey, cultureId)?.LanValue });

        var result = await exporter.ExportAsByteArray(list.ToList()).ConfigureAwait(false);

        return File(result, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", $"{"Language Packs"} {DateTime.Now:yyyyMMddhhmm}.xlsx");
    }

    /// <summary>
    /// 导入翻译项
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize((PermissionFlags)32)]
    [DisplayName("导入")]
    public async Task<IActionResult> Import(IFormFile file)
    {
        var res = new DResult();

        if (file == null)
        {
            res.msg = GetResource("导入文件有误");
            return Json(res);
        }

        var OrignfileName = file.FileName;

        if (OrignfileName.IndexOf("xlsx") == -1)
        {
            res.msg = GetResource("文件名称格式不对");
            return Json(res);
        }

        IExcelImporter Importer = new ExcelImporter();
        var import = await Importer.Import<LocalStringImport>(file.OpenReadStream()).ConfigureAwait(false);

        foreach (var item in import.Data)
        {
            var modelLanguage = Language.FindByName(item.Culture);
            if (modelLanguage != null)
            {
                var model = LocaleStringResource.FindByLanKeyAndCultureId(item.LanKey, modelLanguage.Id);
                if (model != null)
                {
                    model.LanValue = item.TransValue;
                    model.Update();
                }
                else
                {
                    try
                    {
                        model = new LocaleStringResource();
                        model.LanKey = item.LanKey;
                        model.CultureId = modelLanguage.Id;
                        model.LanValue = item.TransValue;
                        model.Insert();
                    }
                    catch (Exception ex)
                    {
                        XTrace.WriteException(ex);
                    }
                }
            }
        }

        res.msg = GetResource("上传成功");
        res.success = true;
        return Json(res);
    }
}
