﻿

using DH.Entity;
using HlktechPower.Entity;



//using DH.Entity;

using Microsoft.AspNetCore.Mvc;

using NewLife;
using NewLife.Data;
using NewLife.Log;

using Pek;
using Pek.DsMallUI;
using Pek.DsMallUI.Common;
using Pek.Ids;
using Pek.Mail;
using Pek.Models;
using Pek.NCubeUI.Areas.Admin;
using Pek.NCubeUI.Common;
using Pek.NCubeUI.Configs;
using Pek.Sms;

using System.ComponentModel;
using System.Dynamic;
using System.Text.Json;
using XCode.Membership;

namespace HlktechPower.Areas.Admin.Controllers;

/// <summary>邮箱短信</summary>
[DisplayName("邮箱短信")]
[Description("用于设置邮箱、短信、微信等消息发送")]
[AdminArea]
[DHMenu(75, ParentMenuName = "Settings", CurrentMenuUrl = "~/{area}/SmsMail", CurrentMenuName = "SmsMailList", CurrentIcon = "&#xe71f;", LastUpdate = "20241217")]
public class SmsMailController : PekCubeAdminControllerX {
    /// <summary>
    /// 邮件设置
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("邮件设置")]
    public IActionResult Index()
    {
        var list = MailSettings.Current.Data;
        return View(list);
    }

    /// <summary>
    /// 邮件平台设置
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("邮件平台设置详情页")]
    public IActionResult EmailSettingDetail(string code)
    {
        XTrace.WriteLine(" -----------------------> "+code);
        var model = MailSettings.Current.FindByCode(code);
        if (model == null)
        {
            return MessageTip(GetResource("指定邮件平台不存在！"));
        }
        return View(model);
    }

    /// <summary>
    /// 新增邮件设置
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Insert)]
    [DisplayName("新增邮件设置")]
    public IActionResult AddEmailSetting()
    {
        return View();
    }

    /// <summary>
    /// 新增邮件设置
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Insert)]
    [DisplayName("新增邮件设置")]
    [HttpPost]
    public IActionResult AddEmailSetting(String email_host, Boolean email_secure, Int32 email_port, String email_addr, String email_id, String email_pass, String fromname, int email_isEnabled, int email_isDefault, string email_Suffix)
    {
        var model = new MailData();
        model.Code = IdHelper.GetIdString();
        model.Host = email_host;
        model.Port = email_port;
        model.UserName = email_id;
        model.Password = email_pass;
        model.From = email_addr;
        model.FromName = fromname;
        model.IsSSL = email_secure;
        model.IsEnabled = email_isEnabled == 1;
        model.IsDefault = email_isDefault == 1;
        model.EmailSuffix = email_Suffix;
        MailSettings.Current.Data.Add(model);
        if (email_isDefault == 1)
        {
            var list = MailSettings.Current.Data;
            foreach (var item in list)
            {
                if (item.Code != model.Code)
                {
                    item.IsDefault = false;
                }
            }
        }
        MailSettings.Current.Save();

        return Prompt(new PromptModel { Message = GetResource("新增成功"), IsOk = true, BackUrl = Url.Action("Index") });
    }

    /// <summary>
    /// 邮件设置
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("邮件设置")]
    public IActionResult UpdateEmailSetting(String Code, String email_host, Boolean email_secure, Int32 email_port, String email_addr, String email_id, String email_pass, String fromname, int email_isEnabled, int email_isDefault, string email_Suffix)
    {
        //var path = @"Settings/Mail.json";
        //ConfigFileHelper.AddOrUpdateAppSetting("Email:Host", email_host, path);
        //ConfigFileHelper.AddOrUpdateAppSetting("Email:Port", email_port, path);
        //ConfigFileHelper.AddOrUpdateAppSetting("Email:UserName", email_id, path);
        //ConfigFileHelper.AddOrUpdateAppSetting("Email:Password", email_pass, path);
        //ConfigFileHelper.AddOrUpdateAppSetting("Email:From", email_addr, path);
        //ConfigFileHelper.AddOrUpdateAppSetting("Email:FromName", fromname, path);
        //ConfigFileHelper.AddOrUpdateAppSetting("Email:IsSSL", email_secure, path);
        var model = MailSettings.Current.FindByCode(Code);
        if (model == null)
        {
            return MessageTip(GetResource("指定邮件平台不存在！"));
        }
        model.Host = email_host;
        model.Port = email_port;
        model.UserName = email_id;
        model.Password = email_pass;
        model.From = email_addr;
        model.FromName = fromname;
        model.IsSSL = email_secure;
        model.IsEnabled = email_isEnabled == 1;
        model.IsDefault = email_isDefault == 1;
        model.EmailSuffix = email_Suffix;
        if (email_isDefault == 1)
        {
            var list = MailSettings.Current.Data;
            foreach (var item in list)
            {
                if (item.Code != model.Code)
                {
                    item.IsDefault = false;
                }
            }
        }
        MailSettings.Current.Save();

        return Prompt(new PromptModel { Message = GetResource("保存成功"), IsOk = true, BackUrl = Url.Action("Index") });
    }

    /// <summary>
    /// 删除邮件平台设置
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Delete)]
    [DisplayName("删除邮件平台设置")]
    public IActionResult DeleteEmailSetting(string codes)
    {
        string[] c = codes.Split(',');
        var res = new DResult();
        foreach (var item in c)
        {
            if (item.IsNullOrWhiteSpace()) continue;
            var model = MailSettings.Current.FindByCode(item);
            if (model == null)
            {
                return MessageTip(GetResource("指定邮件平台不存在！"));
            }
            MailSettings.Current.Data.Remove(model);
        }
        MailSettings.Current.Save();

        res.success = true;
        res.code = 10000;
        res.msg = GetResource("删除成功");
        return Json(res);
    }

    /// <summary>
    /// 短信平台设置
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("短信平台设置")]
    public IActionResult MobileSetting()
    {
        var list = SmsSettings.Current.Data.OrderBy(e => e.Order).ToList();
        return View(list);
    }

    /// <summary>
    /// 短信平台设置
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("短信平台设置详情页")]
    public IActionResult MobileSettingDetail(string code)
    {
        var model = SmsSettings.Current.FindByCode(code);
        if (model == null)
        {
            return MessageTip(GetResource("指定短信平台不存在！"));
        }

        // 解析 ExtendFields 和 ExtendData
        var extendFields = model.ExtendFields?.Split(',') ?? Array.Empty<string>();
        foreach (var field in extendFields)
        {
            XTrace.WriteLine("field" + field);
        }

        // 检查 ExtendData 是否为空或仅包含空白字符
        var extendDataJson = string.IsNullOrWhiteSpace(model.ExtendData) ? "{}" : model.ExtendData;
        var extendData = JsonSerializer.Deserialize<Dictionary<string, object>>(extendDataJson);
        foreach (var field in extendData)
        {
            XTrace.WriteLine("field" + field.Key + "value" + field.Value);
        }

        // 获取有交集的字段
        var intersectFields = extendFields.Intersect(extendData.Keys).ToDictionary(field => field, field => extendData[field]);
        foreach (var field in intersectFields)
        {
            XTrace.WriteLine("交集field" + field.Key + "value" + field.Value);
        }

        // 将交集字段传递到视图
        ViewBag.IntersectFields = intersectFields;

        return View(model);
    }

    /// <summary>
    /// 提交短信平台设置
    /// </summary>
    /// <param name="name"></param>
    /// <param name="displayName"></param>
    /// <param name="timeout"></param>
    /// <param name="security"></param>
    /// <param name="retryTimes"></param>
    /// <param name="extendFields"></param>
    /// <param name="smscf_sign"></param>
    /// <param name="order"></param>
    /// <param name="code"></param>
    /// <param name="smscf_wj_username"></param>
    /// <param name="smscf_wj_key"></param>
    /// <param name="sms_isEnabled"></param>
    /// <param name="sms_type"></param>
    /// <param name="sms_isDefault"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("提交短信平台设置")]
    [HttpPost]
    public IActionResult MobileSettingSave(string name, string displayName, int timeout, int security, int retryTimes, string extendFields, String smscf_sign, int order, String code, String smscf_wj_username, String smscf_wj_key, Int32 sms_isEnabled, int sms_type, int sms_isDefault)
    {
        ////string type;
        ////if (smscf_type == "fenghuo")
        ////{
        ////    type = "FengHuoSms";
        ////}
        ////else
        ////{
        ////    type = "LianLuSms";
        ////}
        ////var path = @"Settings/Sms.json";

        ////ConfigFileHelper.AddOrUpdateAppSetting($"{type}:AccessKeyId", smscf_wj_username, path);
        ////ConfigFileHelper.AddOrUpdateAppSetting($"{type}:AccessKeySecret", smscf_wj_key, path);
        ////ConfigFileHelper.AddOrUpdateAppSetting($"{type}:passKey", smscf_sign, path);
        ////ConfigFileHelper.AddOrUpdateAppSetting($"{type}:IsEnabled", sms_isEnabled == 1, path);
        ////ConfigFileHelper.AddOrUpdateAppSetting($"{type}:SmsLogin", sms_login == 1, path);
        ////ConfigFileHelper.AddOrUpdateAppSetting($"{type}:SmsRegister", sms_register == 1, path);
        ////ConfigFileHelper.AddOrUpdateAppSetting($"{type}:SmsPassword", sms_password == 1, path);

        var model = SmsSettings.Current.FindByCode(code);
        if (model == null)
        {
            return MessageTip(GetResource("指定短信平台不存在！"));
        }

        model.Name = name;
        model.DisplayName = displayName;
        model.AccessKey = smscf_wj_username;
        model.AccessSecret = smscf_wj_key;
        model.SignName = smscf_sign;
        model.Order = order;
        model.Timeout = timeout;
        model.Security = security == 1;
        model.RetryTimes = retryTimes;

        model.ExtendFields = extendFields;//,分割
        var extendFieldsArray = string.IsNullOrWhiteSpace(extendFields) ? Array.Empty<string>() : extendFields.Split(',');
        var extendDataDict = new Dictionary<string, object>();
        foreach (var field in extendFieldsArray)
        {
            var value = GetRequest(field).SafeString().Trim();
            extendDataDict[field] = value;
        }
        model.ExtendData = JsonSerializer.Serialize(extendDataDict);

        //var list = SmsSettings.Current.Data.FindAll(e => e.SmsType == model.SmsType);
        //foreach (var item in list)
        //{
        //    item.IsEnabled = false;
        //}

        model.IsEnabled = sms_isEnabled == 1;
        model.SmsType = sms_type;
        model.IsDefault = sms_isDefault == 1;
        if (sms_isDefault == 1)
        {
            var list = SmsSettings.Current.Data;
            foreach (var item in list)
            {
                if (item.SmsType == sms_type && item.Code != model.Code)
                {
                    item.IsDefault = false;
                }
            }
        }
        //model.AppId = AppId;
        SmsSettings.Current.Save();

        return Prompt(new PromptModel { Message = GetResource("保存成功"), IsOk = true, BackUrl = Url.Action("MobileSetting") });
    }

    /// <summary>
    /// 短信记录
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("短信记录")]
    public IActionResult SmsLogs(string member_name, string smslog_phone, string add_time_from, string add_time_to, int page)
    {
        dynamic viewModel = new ExpandoObject();

        member_name = member_name.SafeString().Trim();
        smslog_phone = smslog_phone.SafeString().Trim();

        viewModel.member_name = member_name;
        viewModel.smslog_phone = smslog_phone;
        viewModel.add_time_from = add_time_from;
        viewModel.add_time_to = add_time_to;

        var pages = new PageParameter()
        {
            PageIndex = page,
            PageSize = 10,
            Sort = SendLog._.CreateTime,
            Desc = true,
            RetrieveTotalCount = true
        };

        var List = SendLog.Searchs(smslog_phone, member_name, add_time_from.ToDateTime(), add_time_to.ToDateTime(), pages);
        viewModel.list = List;
        viewModel.count = List.Count;
        viewModel.Str = PageHelper.CreatePage(page, pages.TotalCount, pages.PageCount, Url.Action("SmsLogs"), new Dictionary<String, String> { { "member_name", member_name }, { "smslog_phone", smslog_phone }, { "add_time_from", add_time_from }, { "add_time_to", add_time_to } });
        return View(viewModel);
    }

    /// <summary>
    /// 短信模板
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("短信模板")]
    //[Obsolete]
    public IActionResult OtherMsgTpls(int page = 1)
    {
        var pages = new PageParameter()
        {
            PageIndex = page,
            PageSize = 10,
            RetrieveTotalCount = true,
            Sort = OtherMsgTpl._.Id,
            Desc = true
        };

        var List = OtherMsgTpl.Searchs(pages).Select(e => new OtherMsgTpl
        {
            Id = e.Id,
            MTitle = (OtherMsgTplLan.FindByOIdAndLId(e.Id,WorkingLanguage.Id)?.MTitle).IsNullOrWhiteSpace() ? e.MTitle : OtherMsgTplLan.FindByOIdAndLId(e.Id, WorkingLanguage.Id)?.MTitle,
            MContent =e.MContent,
            SmsTplId = e.SmsTplId,
            MCode = e.MCode,
            MName = (OtherMsgTplLan.FindByOIdAndLId(e.Id, WorkingLanguage.Id)?.MName).IsNullOrWhiteSpace() ? e.MName : OtherMsgTplLan.FindByOIdAndLId(e.Id, WorkingLanguage.Id)?.MName,
        });
        dynamic viewModel = new ExpandoObject();
        viewModel.list = List;
        viewModel.Str = PageHelper.CreatePage(page, pages.TotalCount, pages.PageCount, Url.Action("OtherMsgTpls"), new Dictionary<String, String> { });
        return View(viewModel);
    }

    /// <summary>
    /// 短信记录删除
    /// </summary>
    /// <param name="Ids"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Delete)]
    [DisplayName("短信记录删除")]
    public IActionResult Delete(string Ids)
    {
        var res = new DResult();
        SendLog.DelByIds(Ids.Trim(','));

        res.success = true;
        res.msg = GetResource("删除成功");
        return Json(res);
    }

    /// <summary>
    /// 修改模板信息
    /// </summary>
    /// <param name="Id"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("修改模板信息")]
    public IActionResult Edit(int Id)
    {
        var Model = OtherMsgTpl.FindById(Id);
        if (Model == null)
        {
            return MessageTip(GetResource("数据不存在或已被删除！"));
        }

        ViewBag.LanguageList = DH.Entity.Language.FindByStatus().OrderBy(e => e.DisplayOrder);

        return View(Model);
    }

    /// <summary>
    /// 修改模板信息
    /// </summary>
    /// <param name="Id"></param>
    /// <param name="MTitle"></param>
    /// <param name="MContent"></param>
    /// <param name="SmsTplId"></param>
    /// <param name="MName"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [HttpPost]
    [DisplayName("修改模板信息")]
    public IActionResult Edit(int Id, string MTitle, string MContent, string SmsTplId, String MName)
    {
        if (MTitle.IsNullOrEmpty())
        {
            return Prompt(new PromptModel { Message = GetResource("标题不能为空") });
        }
        if (MContent.IsNullOrEmpty())
        {
            return Prompt(new PromptModel { Message = GetResource("正文名不能为空") });
        }
        var Model = OtherMsgTpl.FindById(Id);
        if (Model == null)
        {
            return Prompt(new PromptModel { Message = GetResource("数据不存在或已被删除!") });
        }
        Model.MTitle = MTitle.SafeString().Trim();
        Model.MContent = MContent;
        Model.SmsTplId = SmsTplId;
        Model.MName = MName;
        Model.Update();

        var languagelist = DH.Entity.Language.FindByStatus().OrderBy(e => e.DisplayOrder); // 获取全部有效语言
        var list = OtherMsgTplLan.FindAllByOId(Id);  // 获取到的当前权限的语言
        using (var tran1 = OtherMsgTplLan.Meta.CreateTrans())
        {
            foreach (var item in languagelist)
            {
                var ex = list.Find(x => x.LId == item.Id);
                if (ex != null)
                {
                    ex.MName = (GetRequest($"[{item.Id}].MName")).SafeString().Trim();
                    ex.MTitle = (GetRequest($"[{item.Id}].MTitle")).SafeString().Trim();
                    ex.MContent = (GetRequest($"[{item.Id}].MContent")).SafeString().Trim();
                    ex.SmsTplId = (GetRequest($"[{item.Id}].SmsTplId")).SafeString().Trim();
                    ex.Update();
                }
                else
                {
                    ex = new OtherMsgTplLan();
                    ex.MName = (GetRequest($"[{item.Id}].MName")).SafeString().Trim();
                    ex.MTitle = (GetRequest($"[{item.Id}].MTitle")).SafeString().Trim();
                    ex.MContent = (GetRequest($"[{item.Id}].MContent")).SafeString().Trim();
                    ex.SmsTplId = (GetRequest($"[{item.Id}].SmsTplId")).SafeString().Trim();

                    if (ex.MName.IsNullOrWhiteSpace() && ex.MTitle.IsNullOrWhiteSpace() && ex.MContent.IsNullOrWhiteSpace() && ex.SmsTplId.IsNullOrWhiteSpace())
                    {
                        continue;
                    }

                    ex.LId = item.Id;
                    ex.OId = Id;
                    ex.Insert();
                }
            }
            tran1.Commit();
        }

        return Prompt(new PromptModel { Message = GetResource("修改成功"), IsOk = true, BackUrl = Url.Action("OtherMsgTpls") });
    }

    /// <summary>
    /// 新增模板信息
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Insert)]
    [DisplayName("新增模板信息")]
    public IActionResult Add()
    {
        ViewBag.LanguageList = DH.Entity.Language.FindByStatus().OrderBy(e => e.DisplayOrder);
        return View();
    }

    [EntityAuthorize(PermissionFlags.Insert)]
    [HttpPost]
    [DisplayName("新增模板信息")]
    public IActionResult Add(string MTitle, string MContent, string SmsTplId, String MName,String MCode)
    {
        if (MTitle.IsNullOrEmpty())
        {
            return Prompt(new PromptModel { Message = GetResource("标题不能为空") });
        }
        if (MContent.IsNullOrEmpty())
        {
            return Prompt(new PromptModel { Message = GetResource("正文名不能为空") });
        }
        if (MCode.IsNullOrEmpty())
        {
            return Prompt(new PromptModel { Message = GetResource("模板标识不能为空") });
        }
        var Model = new OtherMsgTpl();
        Model.MTitle = MTitle.SafeString().Trim();
        Model.MContent = MContent;
        Model.SmsTplId = SmsTplId;
        Model.MName = MName;
        Model.MCode = MCode;
        Model.Insert();

        var languagelist = DH.Entity.Language.FindByStatus().OrderBy(e => e.DisplayOrder); // 获取全部有效语言
        var list = OtherMsgTplLan.FindAll();  // 获取到的当前权限的语言
        using (var tran1 = OtherMsgTplLan.Meta.CreateTrans())
        {
            foreach (var item in languagelist)
            {
                var ex = list.Find(x => x.LId == item.Id);
                if (ex != null)
                {
                    ex.MName = (GetRequest($"[{item.Id}].MName")).SafeString().Trim();
                    ex.MTitle = (GetRequest($"[{item.Id}].MTitle")).SafeString().Trim();
                    ex.MContent = (GetRequest($"[{item.Id}].MContent")).SafeString().Trim();
                    ex.SmsTplId = (GetRequest($"[{item.Id}].SmsTplId")).SafeString().Trim();
                    ex.Update();
                }
                else
                {
                    ex = new OtherMsgTplLan();
                    ex.MName = (GetRequest($"[{item.Id}].MName")).SafeString().Trim();
                    ex.MTitle = (GetRequest($"[{item.Id}].MTitle")).SafeString().Trim();
                    ex.MContent = (GetRequest($"[{item.Id}].MContent")).SafeString().Trim();
                    ex.SmsTplId = (GetRequest($"[{item.Id}].SmsTplId")).SafeString().Trim();

                    if (ex.MName.IsNullOrWhiteSpace() && ex.MTitle.IsNullOrWhiteSpace() && ex.MContent.IsNullOrWhiteSpace() && ex.SmsTplId.IsNullOrWhiteSpace())
                    {
                        continue;
                    }

                    ex.LId = item.Id;
                    ex.OId = Model.Id;
                    ex.Insert();
                }
            }
            tran1.Commit();
        }

        return Prompt(new PromptModel { Message = GetResource("新增成功"), IsOk = true, BackUrl = Url.Action("OtherMsgTpls") });
    }

    /// <summary>
    /// 短信模板删除
    /// </summary>
    /// <param name="Ids"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Delete)]
    [DisplayName("短信模板删除")]
    public IActionResult Delete2(string Ids)
    {
        var res = new DResult();
        OtherMsgTpl.DelByIds(Ids.Trim(','));

        res.success = true;
        res.msg = GetResource("删除成功");
        return Json(res);
    }
}
