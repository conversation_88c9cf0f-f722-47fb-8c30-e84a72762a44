<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>侧边栏测试</title>
    <link rel="stylesheet" href="css/public.css">
    <link rel="stylesheet" href="css/mySidebar.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .container {
            display: flex;
            gap: 20px;
            max-width: 1200px;
            margin: 0 auto;
        }

        .content {
            flex: 1;
            background: white;
            padding: 20px;
            border-radius: 8px;
        }

        h1 {
            color: #333;
            margin-bottom: 20px;
        }

        .info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
    </style>
</head>

<body>
    <div class="container">
        <!-- 侧边栏容器 -->
        <div class="mySidebar"></div>

        <!-- 内容区域 -->
        <div class="content">
            <h1>侧边栏点击高亮测试</h1>

            <div class="info">
                <h3>功能说明：</h3>
                <ul>
                    <li>点击左侧菜单项，该项会高亮显示</li>
                    <li>同时会执行页面跳转（这里用alert模拟）</li>
                    <li>只有被点击的项保持高亮状态</li>
                    <li>悬停时有颜色变化效果</li>
                </ul>
            </div>

            <p>请点击左侧侧边栏的菜单项来测试功能。</p>
        </div>
    </div>

    <!-- 模拟数据 -->
    <script>
        // 模拟侧边栏数据
        const data = [
            { text: "应用支持", link: "/appSupport/" },
            { text: "焦点专题", link: "/focus/" },
            { text: "资料下载", link: "/downloads/" },
            { text: "应用视频", link: "/videos/" },
            { text: "常见问题", link: "/faq/" },
            { text: "样品申请", link: "/sample/" }
        ];

        // 模拟toRouter函数
        function toRouter(element) {
            const link = element.getAttribute('data-link');
            const menuItem = element.getAttribute('data-menu-item');

            console.log('toRouter被调用:', { element, link, menuItem });

            // 这里用alert模拟跳转，实际使用时会进行真实的页面跳转
            alert(`点击了: ${menuItem}\n将跳转到: ${link}`);

            // 实际项目中这里会是：
            // window.location.href = link;
        }
    </script>

    <!-- 引入侧边栏组件 -->
    <script src="components/mySidebar.js"></script>
</body>

</html>