 /* 首页 */

 body {
   background-color: #eff1f5;
 }

 .mainBox0 {
   width: 1340px;
   margin: 0 auto;
   background-color: #EFF1F5;
   overflow-x: hidden;
   padding-bottom: 50px;

 }




 .supportBox {
   position: relative;
   cursor: pointer;
 }

 .supportBox:hover .support {
   display: block;
   border-bottom: 4px solid var(--white);
 }

 .support {
   display: none;
   /* display: block; */
   width: 120px;
   position: absolute;
   top: 100%;
   left: 50%;
   transform: translateX(-50%);
   margin-top: 4px;
   color: #000;
   background-color: var(--white);
   font-size: 16px;
   border-radius: 5px;
   padding: 15px;
   box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
   z-index: 1000;
 }

 .support p {
   margin: 8px 0;
   padding: 5px;
   border-radius: 4px;
   transition: all 0.2s ease;
   text-align: center;
 }

 .support p:hover {
   background-color: #0E6AF21A;
   color: var(--blue-deep);
 }



 .supportBox:hover .support2 {
   display: flex;
   align-content: center;
   border-bottom: 4px solid var(--white);
 }

 .support2 {
   width: 1200px;
   display: none;
   position: absolute;
   top: 100%;
   left: -538px;
   transform: translateX(-50%);
   margin-top: 4px;
   color: #000;
   background-color: var(--white);
   font-size: 16px;
   border-radius: 5px;
   padding: 15px;
   box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
   z-index: 1000;
 }

 .support2 ul {
   margin: 0 30px;
 }

 .support2 ul li {
   padding: 5px 10px;
 }

 .support2 ul li:first-child {
   font-weight: bold;
   border-bottom: 1px solid #c0c0c0;
 }


 .footer {
   background-color: #445268;
   border: 1px solid #000;
 }

 .footer2>div>div:nth-child(1) {
   color: white;
 }

 .footer1Content>div {
   text-align: left;
 }

 .footer1Content>div:hover {
   color: white;
 }

 .productCategory[data-select="true"] {
   color: var(--blue-deep);
   border-bottom: 1px solid;
 }

 /* z账户 */
 .acountBox:hover .acountInfo {
   display: flex;
 }

 .acountInfo {
   position: absolute;
   top: 90%;
   width: 150%;
   padding: 1vw;
   border: 1px solid var(--line);
   box-shadow: 4px 2px 8px 0px rgba(0, 0, 0, 0.15);
   border-radius: 24px 24px 24px 24px;
   border: 1px solid #E2E3E9;
   display: flex;
   flex-direction: column;
   background-color: white;
   z-index: 99999999999999;
   display: none;
 }

 .acountHead {
   /* padding: .2vw 0px 1vw 0px; */
   height: 100%;
   /* border-bottom: 1px solid var(--line); */
 }

 .acountHead>div:nth-child(1) {
   width: 30%;
   margin-right: auto;
 }

 .acountHead>div:nth-child(2) {
   width: 65%;
   /* border: 1px solid ; */
 }

 .acountHead>div>img {
   width: 100%;
 }

 .acountArea>div>.icon {
   margin-right: .7vw;
 }

 .acountArea {
   padding-bottom: 1vw;
   margin: 5px;
   border-bottom: 1px solid var(--line);
 }

 .acountArea>div {
   padding: .25vw 0px;
   font-size: .75vw;
   display: flex;
   justify-content: left;
   place-items: center;
 }

 .acountArea>div:hover {
   color: var(--blue-deep);
   cursor: pointer;
 }

 .loginOutBox>div {
   padding: .5vw;
   border-radius: 20px;
   cursor: pointer;
   text-align: center;
   box-sizing: border-box;
   border: 1px solid transparent;
 }




 
 .hoverPages {
   position: relative;
   left: 100%;
   width: 396%;
   height: 100%;
   max-height: 40vw;
   background-color: white;
   overflow: hidden;
   display: none;
   z-index: 2;
   border-radius: 0px 25px 25px 0px;
   border: 1px solid var(--line);
   border-radius: 10px;
 }

 .hoverPages>div>div:hover {
   color: var(--blue-deep);

 }

 .hoverPages:hover {
   display: flex;
 }

 .hoverPages>div>div {
   padding: 15px 0px;
   display: flex;
   justify-content: space-between;
   color: var(--text-color);
   cursor: pointer;
   white-space: nowrap;
   text-align: left;
   font-size: 1em;
   text-indent: 1.7cqw;
 }

 .twoPage {
   width: 35vw;
   overflow-y: scroll;
   border: 1px solid #72ba98;
 }

 .twoPage>div[data-select="true"] {
   color: var(--blue-deep);

 }

 .onePage>div[data-select="true"] {
   color: var(--blue-deep);
 }

 .onePage::-webkit-scrollbar {
   width: .3vw;
 }

 .onePage::-webkit-scrollbar-track {
   width: 6px;
   background: rgba(#101F1C, 0.1);
   -webkit-border-radius: 2em;
   -moz-border-radius: 2em;
   border-radius: 2em;
 }

 .onePage::-webkit-scrollbar-thumb {
   background-color: #f2f2f2;
   background-clip: padding-box;
   min-height: 28px;
   -webkit-border-radius: 2em;
   -moz-border-radius: 2em;
   border-radius: 2em;
   transition: background-color .3s;
   cursor: pointer;
 }

 .onePage::-webkit-scrollbar-thumb:hover {
   background-color: rgba(144, 147, 153, .3);
 }



 .rightIcon {
   font-size: 20px;
   margin-left: auto;
   margin-right: 1.0em;
 }

 .mainBox1_center {
   display: flex;
   flex-direction: column;
   width: 80%;
   height: 100%;
 }

 .mainBox1_center>div:nth-child(1) {
   width: 100%;
   height: 75%;
   /* background-color: red; */
   display: flex;
 }


 .cardBox {
   width: 1340px;
   margin: 20px auto;
   padding-top: 20px;
   display: flex;
   justify-content: space-between;
   gap: 24px;
   /* padding: 40px 0; */

 }

 .card {
   flex: 1;
   min-width: 220px;
   max-width: 350px;
   height: 138px;
   border-radius: 5px;
   color: #fff;
   position: relative;
   box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
   background-size: cover;
   background-position: right bottom;
   background-repeat: no-repeat;
   transition: transform 0.2s;
   margin-left: 10px;
 }

 .card h2,
 .card p {
   margin-top: 10px;
   margin-left: 10px;

 }

 .card:hover {
   transform: translateY(-6px) scale(1.03);
   box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
 }

 /* 分别设置每个卡片的背景图和主色 */
 .card1 {
   background: url('../../images/power/chanpinzhongxin.png') right bottom no-repeat;
 }

 .card2 {
   background: url('../../images/power/chanpinxuanxing.png') right bottom no-repeat;
 }

 .card3 {
   background: url('../../images/power/yangpinshenqing.png') right bottom no-repeat;
 }

 .card4 {
   background: url('../../images/power/lianxiwomen.png') right bottom no-repeat;
 }


 .news-section {
   display: flex;
   justify-content: space-between;
   gap: 32px;
   margin: 20px;
 }

 .title {
   display: flex;
   align-items: center;
   justify-content: space-between;
   margin-bottom: 24px;
 }

 .wen {
   font-size: 26px;
 }

 .more-link {
   color: #777C89;
   font-size: 14px;
 }

 .news-card {
   flex: 1;
   background: #fff;
   border-radius: 24px;
   box-shadow: 0 4px 24px 0 rgba(0, 0, 0, 0.08);
   padding: 10px 0 10px 0;
   display: flex;
   flex-direction: column;
   width: 400px;
 }

 .news-card.blue {
   background: var(--myBlue);
   color: #fff;
 }


 .news-card.blue2 {
   background: #1664BE;
   color: #fff;
 }



 .news-card h2 {
   font-size: 2rem;
   font-weight: bold;
   margin-bottom: 8px;
   margin-left: 32px;
 }

 .news-card .more-link {
   font-size: 1rem;
   color: #999;
   text-align: right;
   margin-right: 32px;
   margin-bottom: 16px;
   cursor: pointer;
 }

 .news-list {
   list-style: none;
   padding: 0 10px;
   margin: 0;
 }

 .news-list li {
   display: flex;
   align-items: center;
   padding: 5px;
   width: 370px;
   border-bottom: 1px solid #f0f0f0;
 }

 .news-card.blue .news-list li {
   border-bottom: 1px solid rgba(255, 255, 255, 0.12);
 }

 .news-list li:last-child {
   border-bottom: none;
 }

 .news-index {
   font-weight: bold;
   margin-right: 16px;
   width: 24px;
   text-align: right;
 }



 .news-card.blue .news-index {
   color: #fff;
   opacity: 0.8;
 }

 .news-title {
   flex: 1;
   white-space: nowrap;
   overflow: hidden;
   text-overflow: ellipsis;
 }

 .news-tagBox {
   width: 60px;
   text-align: center;
 }

 .news-tag {
   margin-left: 12px;
   font-size: 12px;
   padding: 2px 10px;
   border-radius: 3px;
   background: #ff4d4f;
   color: #fff;
   font-weight: bold;


 }

 .news-tag.new {
   background: #ffb400;
   color: #fff;

 }

 .news-tag.hot {
   background: #ff4d4f;
   color: #fff;
 }



 /* 产品中心样式 */
 .productCenter {
   margin: 20px 0;
 }

 .productCenter h2 {
   text-align: left;
   font-size: 26px;
   margin: 10px 0;
   color: #333;
   font-weight: normal;
 }

 .productContainer {
   display: flex;
   margin: 0 auto;
   height: 392px;
 }


 .productBanner {
   width: 20%;
   display: flex;
   flex-direction: column;
   align-items: center;
   justify-content: center;
   background: url('../../images/power/chakangengduo.png') center no-repeat;
   background-color: var(--myBlue);
 }

 .productBanner img {
   max-width: 80%;
   margin-bottom: 2rem;
 }

 .viewMore {
   display: inline-block;
   padding: 0.5rem 1.5rem;
   border: 2px solid var(--white);
   color: var(--white);
   border-radius: 20px;
   text-decoration: none;
   font-weight: bold;
 }

 .productGrid {
   width: 80%;
   display: grid;
   grid-template-columns: repeat(4, 1fr);
 }

 .productItem {
   background-color: white;
   padding: 1rem;
   border-bottom: 1px solid #dddddd;
   border-right: 1px solid #dddddd;
   height: 166px;
 }

 .noBottom {
   border-bottom: 1px solid transparent;
 }

 .productItem h3 {
   color: #1e88e5;
   margin-bottom: 0.5rem;
   font-size: 1rem;
 }

 .myline {
   width: 20px;
   height: 2px;
   background-color: #777C89;
   margin-bottom: 5px;
 }

 .productItem p {
   color: #666;
   font-size: 0.85rem;
   min-height: 2.5em;

 }

 .productImage {
   width: 55%;
   height: 55%;
   display: block;
   margin-left: 50%;
   object-fit: contain;
 }


 .topicBox {
   display: flex;
   gap: 20px;
   align-items: center;
   height: 426px;

 }

 .topic {
   width: 40%;
   display: flex;
   flex-direction: row;
   flex-wrap: wrap;
   gap: 15px;
   height: 424px;
   /* border: 1px solid #f12424; */
 }

 .topic li {
   font-weight: 600;
   /* border-radius: 2px; */
   width: 250px;
   height: 130px;
   display: flex;
   flex-direction: column;
   align-items: center;
   justify-content: center;
   background-color: #fff;
   gap: 15px;
 }

 /* 默认图标 */
 .icon {
   width: 34px;
   height: 34px;
   background-position: center;
   background-repeat: no-repeat;
   background-size: cover;
 }


 .li1 .icon {
   background: url('../../images/power/gongyekongzhi_moren.png');
 }

 .li2 .icon {
   background: url('../../images/power/guojiadianwang-moren.png');
 }

 .li3 .icon {
   background: url('../../images/power/guidaojiaotong_moren.png');
 }

 .li4 .icon {
   background: url('../../images/power/wulianwang_moren.png');
 }

 .li5 .icon {
   background: url('../../images/power/qichedianzi_moren.png');
 }



 .topic li:hover,
 .topic .icon:hover {
   background-position: center;
   background-repeat: no-repeat;
   color: var(--white);
   background-size: cover;

 }

 /* 图标高亮 */
 .topic .li1:hover .icon {
   background-image: url('../../images/power/gongyekongzhi_gaoliang.png');
 }

 .topic .li2:hover .icon {
   background-image: url('../../images/power/guojiadianwang_gaoliang.png');
 }

 .topic .li3:hover .icon {
   background-image: url('../../images/power/guidaojiaotong_gaoliang.png');
 }

 .topic .li4:hover .icon {
   background-image: url('../../images/power/wulianwang_gaoliang.png');
 }

 .topic .li5:hover .icon {
   background-image: url('../../images/power/qichedianzi_gaoliang.png');
 }



 /* 背景图 */
 .topic .li1:hover {
   background-image: linear-gradient(rgba(14, 106, 242, 0.5), rgba(10, 88, 205, 0.8)), url('../../images/power/jiaodianzhuanti.png');
 }

 .topic .li2:hover {
   background-image: linear-gradient(rgba(14, 106, 242, 0.5), rgba(10, 88, 205, 0.8)), url('../../images/power/dianwang_bg.png');
 }

 .topic .li3:hover {
   background-image: linear-gradient(rgba(14, 106, 242, 0.5), rgba(10, 88, 205, 0.8)), url('../../images/power/guidao_bg.png');
 }

 .topic .li4:hover {
   background-image: linear-gradient(rgba(14, 106, 242, 0.5), rgba(10, 88, 205, 0.8)), url('../../images/power/wulianwang_bg.png');
 }

 .topic .li5:hover {
   background-image: linear-gradient(rgba(14, 106, 242, 0.5), rgba(10, 88, 205, 0.8)), url('../../images/power/qiche_bg.png');
 }


 .topic li:hover img {
   background-color: #fff;
 }

 .topicImgBox {
   width: 60%;
   height: 426px;
   position: relative;

 }

 .topicImg {
   width: 100%;
   height: 426px;

 }

 .bg {
   width: 100%;
   position: absolute;
   bottom: 0px;
   display: flex;
   flex-direction: column;
   gap: 15px;
   background-color: #1B283BCC;
   color: var(--white);
   font-size: 16px;
   padding: 15px 0;
   text-indent: 2rem;
   border-radius: 0 0 5px 5px;
 }












 .noticeBox>div {
   padding: 5px 0px;
   cursor: pointer;
 }

 .swiper-box {
   width: 100%;
   height: 518px;
   position: relative;
   overflow: hidden;
 }

 /* 轮播开始 */
 .swiper-container {
   width: 100%;
   height: 100%;
   position: relative;
   overflow: hidden;
 }

 .swiper-wrapper {
   width: 100%;
   height: 100%;
   position: relative;
   display: flex;
 }

 .swiper-slide {
   width: 100%;
   height: 100%;
   position: relative;
   flex-shrink: 0;
 }

 .swiper-slide>img {
   width: 100%;
   height: 100%;
   /* object-fit: contain; */
 }

 .pagination {
   position: absolute;
   z-index: 20;
   bottom: 10px;
   width: 100%;
   text-align: center;
 }

 .swiper-pagination-switch {
   display: inline-block;
   width: 8px;
   height: 8px;
   border-radius: 8px;
   background: #bbbaba;
   margin: 0 5px;
   opacity: 0.8;
   border: 1px solid #fff;
   cursor: pointer;
 }

 .swiper-active-switch {
   background: dodgerblue;
 }


















