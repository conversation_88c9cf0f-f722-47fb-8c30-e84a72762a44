/* 统一侧边栏样式 - 使用 .mySidebar li 统一管理 */
.mySidebar {
    width: 18%;
    border: 1px solid var(--myBorder);
    cursor: pointer;
    background: #fff;
}

/* 侧边栏容器样式 */
.mySidebar ul {
    list-style: none;
    padding: 0;
    margin: 0;
    background: #fff;
}

/* 侧边栏标题样式 */
.mySidebar h2 {
    padding: 10px 20px;
    height: 40px;
    line-height: 40px;
    font-weight: bolder;
    font-size: 18px;
    background: #0E6AF233;
    margin: 0;
    color: #333;
}

/* 侧边栏菜单项样式 - 统一使用 .mySidebar li */
.mySidebar li {
    padding: 10px 20px;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
}

/* 悬停效果 */
.mySidebar li:hover {
    color: var(--myBlue);
    background-color: var(--myBlueBg);
}

/* 激活状态样式 */
.mySidebar li.textSelect {
    color: var(--myBlue) !important;
    background-color: var(--myBlueBg);
    font-weight: 500;
}

/* 响应式样式 */
@media screen and (max-width: 768px) {
    .mySidebar {
        width: 100%;
        margin-bottom: 20px;
    }
}