.dataDown{
  display: flex;
  gap: 1vw;
}
.breadBox {
  border-bottom: none;
}
.h3{
  font-size: 1.2vw;
}
.dataDown-right{
  flex: 1;
  
}
.search{
  display: flex;
  justify-content: space-between;
  margin-right: 50px;
  padding-bottom: 1ch;
  border-bottom: 1px solid var(--line);
  border-radius: 5px;
  margin-bottom: 20px;
  overflow: hidden;
}
.layui-input{
  border-color: var(--myBlue);
}
.layui-input-suffix{
  color: var(--myBlue);
  background: var(--myBlue);
  border: 1px solid var(--myBlue);
  border-radius: 0 0 5px;
}
.dataDown-right  .icon-search,.wen{
  color: white;
}

.layui-tab-brief>.layui-tab-title .layui-this {
  color: var(--white);
  background-color:var(--myBlue);
  
}

.layui-tab .layui-tab-title li{
  margin: 0 20px;
  border: 1px solid var(--line);
}
.layui-tab-content{
  margin-top: 20px;
}
.layui-tab-content2{
  margin-top: 10px;
}

.layui-tab .layui-tab-title:after {
  bottom: -20px;
  border-bottom-color: var(--myBlue);
  border-bottom-width: 2px;
}
.item li{
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 140px 10px 40px;
  border-bottom: 1px solid var(--line); 
  font-size: 14px;
}
.item li:first-child{
  padding: 15px 140px 15px 40px;
}
/* 取消下划线 */
.layui-tab-title .layui-this:after {
  width: 0;
}

.item2 li{
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 10px 140px 10px 20px;
  border-bottom: 1px solid var(--line); 
  font-size: 14px;
}